"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/ui/logbook/components/logbook-dropdown.tsx":
/*!************************************************************!*\
  !*** ./src/app/ui/logbook/components/logbook-dropdown.tsx ***!
  \************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LogbookActionMenu: function() { return /* binding */ LogbookActionMenu; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./src/components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* harmony import */ var nuqs__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! nuqs */ \"(app-pages-browser)/./node_modules/.pnpm/nuqs@2.4.3_next@14.2.30_@ba_987e738c28daf1a1a086933c5a55be9a/node_modules/nuqs/dist/index.js\");\n/* harmony import */ var _app_lib_icons__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/lib/icons */ \"(app-pages-browser)/./src/app/lib/icons/index.ts\");\n/* harmony import */ var _app_lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/lib/utils */ \"(app-pages-browser)/./src/app/lib/utils.ts\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_8__);\n/* __next_internal_client_entry_do_not_use__ LogbookActionMenu auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction LogbookActionMenu(param) {\n    let { items, onBack, onDistructAction, ShowDistructive = false, setOpen, logBookConfig, backLabel = \"Back to vessel\", disTructLabel = \"Delete logbook entry\", onSelect, triggerIcon, openCreateTaskSidebar = false, setOpenCreateTaskSidebar, forecast, setForecast, logBookEntryID, setIsWriteModeForecast, openTripSelectionDialog, setOpenTripSelectionDialog, doCreateTripReport } = param;\n    _s();\n    // Use nuqs to manage the tab state through URL query parameters\n    const [tab, setTab] = (0,nuqs__WEBPACK_IMPORTED_MODULE_9__.useQueryState)(\"tab\", {\n        defaultValue: \"crew\"\n    });\n    const [radioLogField, setRadioLogField] = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_7__.useRouter)();\n    const handleSelect = async (item)=>{\n        if (item.url) {\n            router.push(item.url);\n        } else if (onSelect) {\n            onSelect(item.value);\n        } else {\n            if (item.value !== tab) {\n                await setTab(item.value);\n            }\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_6__.useEffect)(()=>{\n        if (logBookConfig) {\n            var _logBookConfig_customisedLogBookComponents_nodes_find_customisedComponentFields_nodes, _logBookConfig_customisedLogBookComponents_nodes_find_customisedComponentFields, _logBookConfig_customisedLogBookComponents_nodes_find, _logBookConfig_customisedLogBookComponents_nodes, _logBookConfig_customisedLogBookComponents;\n            setRadioLogField(logBookConfig === null || logBookConfig === void 0 ? void 0 : (_logBookConfig_customisedLogBookComponents = logBookConfig.customisedLogBookComponents) === null || _logBookConfig_customisedLogBookComponents === void 0 ? void 0 : (_logBookConfig_customisedLogBookComponents_nodes = _logBookConfig_customisedLogBookComponents.nodes) === null || _logBookConfig_customisedLogBookComponents_nodes === void 0 ? void 0 : (_logBookConfig_customisedLogBookComponents_nodes_find = _logBookConfig_customisedLogBookComponents_nodes.find((node)=>node.title === \"Trip Log\")) === null || _logBookConfig_customisedLogBookComponents_nodes_find === void 0 ? void 0 : (_logBookConfig_customisedLogBookComponents_nodes_find_customisedComponentFields = _logBookConfig_customisedLogBookComponents_nodes_find.customisedComponentFields) === null || _logBookConfig_customisedLogBookComponents_nodes_find_customisedComponentFields === void 0 ? void 0 : (_logBookConfig_customisedLogBookComponents_nodes_find_customisedComponentFields_nodes = _logBookConfig_customisedLogBookComponents_nodes_find_customisedComponentFields.nodes) === null || _logBookConfig_customisedLogBookComponents_nodes_find_customisedComponentFields_nodes === void 0 ? void 0 : _logBookConfig_customisedLogBookComponents_nodes_find_customisedComponentFields_nodes.find((node)=>node.fieldName === \"RadioLog\"));\n        }\n    }, [\n        logBookConfig\n    ]);\n    const handleSetTask = ()=>{\n        setOpenCreateTaskSidebar(true);\n    };\n    const getTimeNow = ()=>{\n        return dayjs__WEBPACK_IMPORTED_MODULE_8___default()().format(\"HH:mm\");\n    };\n    const getDayNow = ()=>{\n        return dayjs__WEBPACK_IMPORTED_MODULE_8___default()().format(\"YYYY-MM-DD\");\n    };\n    const initForecast = ()=>{\n        setForecast({\n            id: 0,\n            time: getTimeNow(),\n            day: getDayNow(),\n            geoLocationID: 0,\n            lat: 0,\n            long: 0,\n            logBookEntryID: logBookEntryID\n        });\n    };\n    const createForecast = ()=>{\n        initForecast();\n        setIsWriteModeForecast(true);\n    };\n    const handleCustomTrip = ()=>{\n        setOpenTripSelectionDialog(false);\n        const input = {\n            logBookEntryID: logBookEntryID\n        };\n        doCreateTripReport(input, false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_1__.DropdownMenu, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_1__.DropdownMenuTrigger, {\n                className: \"h-10 flex items-center\",\n                asChild: !!triggerIcon,\n                children: triggerIcon ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                    variant: \"ghost\",\n                    onClick: (e)=>e.stopPropagation(),\n                    size: \"sm\",\n                    className: \"h-8 w-8 p-0\",\n                    children: triggerIcon\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\logbook-dropdown.tsx\",\n                    lineNumber: 141,\n                    columnNumber: 21\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_lib_icons__WEBPACK_IMPORTED_MODULE_3__.SealogsCogIcon, {\n                    size: 36\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\logbook-dropdown.tsx\",\n                    lineNumber: 149,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\logbook-dropdown.tsx\",\n                lineNumber: 137,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_1__.DropdownMenuContent, {\n                align: \"end\",\n                className: \"w-[256px] p-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-input flex flex-col items-center justify-center py-[9px]\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_1__.DropdownMenuItem, {\n                                variant: \"backButton\",\n                                onClick: onBack,\n                                children: backLabel\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\logbook-dropdown.tsx\",\n                                lineNumber: 154,\n                                columnNumber: 21\n                            }, this),\n                            items.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_1__.DropdownMenuItem, {\n                                            onClick: ()=>handleSelect(item),\n                                            children: [\n                                                !item.icon ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-full w-fit flex z-10 relative flex-col items-center justify-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"border-l -translate-y-[17.5px] absolute inset-y-0 border-wedgewood-200 border-dashed\", index === 0 && \"invisible\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\logbook-dropdown.tsx\",\n                                                            lineNumber: 166,\n                                                            columnNumber: 41\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"size-[11px] z-10 rounded-full\", \"group-hover:border-primary group-hover:bg-curious-blue-200\", tab === item.value ? \"border border-primary bg-curious-blue-200\" : \"border border-cool-wedgewood-200 bg-outer-space-50\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\logbook-dropdown.tsx\",\n                                                            lineNumber: 172,\n                                                            columnNumber: 41\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\logbook-dropdown.tsx\",\n                                                    lineNumber: 165,\n                                                    columnNumber: 37\n                                                }, this) : item.icon,\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"relative z-20\", tab === item.value && !item.url ? \"font-medium text-accent-foreground\" : \"\"),\n                                                    children: item.label\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\logbook-dropdown.tsx\",\n                                                    lineNumber: 185,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\logbook-dropdown.tsx\",\n                                            lineNumber: 160,\n                                            columnNumber: 29\n                                        }, this),\n                                        tab === \"pre-departure-checks\" && tab === item.value && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_1__.DropdownMenuItem, {\n                                            onClick: handleSetTask,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-full w-fit flex z-10 relative flex-col items-center justify-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"border-l -translate-y-[17.5px] absolute inset-y-0 border-wedgewood-200 border-dashed\", index === 0 && \"invisible\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\logbook-dropdown.tsx\",\n                                                            lineNumber: 199,\n                                                            columnNumber: 45\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"size-[11px]\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\logbook-dropdown.tsx\",\n                                                            lineNumber: 205,\n                                                            columnNumber: 45\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\logbook-dropdown.tsx\",\n                                                    lineNumber: 198,\n                                                    columnNumber: 41\n                                                }, this),\n                                                \"Add task\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\logbook-dropdown.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 37\n                                        }, this),\n                                        tab === \"weather\" && tab === item.value && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_1__.DropdownMenuItem, {\n                                            onClick: ()=>createForecast(),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-full w-fit flex z-10 relative flex-col items-center justify-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"border-l -translate-y-[17.5px] absolute inset-y-0 border-wedgewood-200 border-dashed\", index === 0 && \"invisible\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\logbook-dropdown.tsx\",\n                                                            lineNumber: 216,\n                                                            columnNumber: 41\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"size-[11px]\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\logbook-dropdown.tsx\",\n                                                            lineNumber: 222,\n                                                            columnNumber: 41\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\logbook-dropdown.tsx\",\n                                                    lineNumber: 215,\n                                                    columnNumber: 37\n                                                }, this),\n                                                \"Add forecast\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\logbook-dropdown.tsx\",\n                                            lineNumber: 213,\n                                            columnNumber: 33\n                                        }, this),\n                                        tab === \"trip-log\" && tab === item.value && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_1__.DropdownMenuItem, {\n                                            onClick: handleCustomTrip,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-full w-fit flex z-10 relative flex-col items-center justify-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"border-l -translate-y-[17.5px] absolute inset-y-0 border-wedgewood-200 border-dashed\", index === 0 && \"invisible\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\logbook-dropdown.tsx\",\n                                                            lineNumber: 230,\n                                                            columnNumber: 41\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"size-[11px]\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\logbook-dropdown.tsx\",\n                                                            lineNumber: 236,\n                                                            columnNumber: 41\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\logbook-dropdown.tsx\",\n                                                    lineNumber: 229,\n                                                    columnNumber: 37\n                                                }, this),\n                                                \"Add trip\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\logbook-dropdown.tsx\",\n                                            lineNumber: 228,\n                                            columnNumber: 33\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\logbook-dropdown.tsx\",\n                                    lineNumber: 159,\n                                    columnNumber: 25\n                                }, this))\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\logbook-dropdown.tsx\",\n                        lineNumber: 153,\n                        columnNumber: 17\n                    }, this),\n                    logBookConfig && radioLogField && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: radioLogField.status !== \"Off\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_2__.Separator, {\n                                    className: \"border-border\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\logbook-dropdown.tsx\",\n                                    lineNumber: 248,\n                                    columnNumber: 33\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_1__.DropdownMenuItem, {\n                                    hoverEffect: false,\n                                    className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"px-[47px] h-[61px] text-text hover:text-foreground flex gap-2.5 group py-[21px] relative focus:bg-outer-space-50 rounded-none\"),\n                                    onClick: ()=>{\n                                        setOpen(true);\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"relative z-20 text-text uppercase hover:text-primary text-sm\",\n                                            children: \"Radio logs\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\logbook-dropdown.tsx\",\n                                            lineNumber: 257,\n                                            columnNumber: 37\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"absolute w-20 left-12 inset-y-0\", \"group-hover:bg-outer-space-50 group-hover:w-full group-hover:left-0\", \"will-change-transform will-change-width will-change-padding transform-gpu\", \"group-hover:transition-[width,left] group-hover:ease-out group-hover:duration-300\", \"outline-none focus:outline-none active:outline-none\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\logbook-dropdown.tsx\",\n                                            lineNumber: 260,\n                                            columnNumber: 37\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\logbook-dropdown.tsx\",\n                                    lineNumber: 249,\n                                    columnNumber: 33\n                                }, this)\n                            ]\n                        }, void 0, true)\n                    }, void 0, false),\n                    ShowDistructive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_2__.Separator, {\n                                className: \"border-border\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\logbook-dropdown.tsx\",\n                                lineNumber: 276,\n                                columnNumber: 25\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_1__.DropdownMenuItem, {\n                                hoverEffect: false,\n                                onClick: onDistructAction,\n                                className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"group relative h-[61px] px-[26px] py-[21px] cursor-pointer focus:bg-destructive/5 rounded-none text-destructive focus:text-destructive\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative gap-2.5 flex items-center z-20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                size: 24\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\logbook-dropdown.tsx\",\n                                                lineNumber: 284,\n                                                columnNumber: 33\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: disTructLabel\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\logbook-dropdown.tsx\",\n                                                lineNumber: 285,\n                                                columnNumber: 33\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\logbook-dropdown.tsx\",\n                                        lineNumber: 283,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"absolute w-full h-11 bottom-0 inset-x-0\", \"group-hover:bg-destructive/focus:bg-destructive/5 group-hover:h-full\", \"will-change-transform will-change-width will-change-padding transform-gpu\", \"group-hover:transition-[height,color] group-hover:ease-out group-hover:duration-300\", \"outline-none focus:outline-none active:outline-none\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\logbook-dropdown.tsx\",\n                                        lineNumber: 287,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\logbook-dropdown.tsx\",\n                                lineNumber: 277,\n                                columnNumber: 25\n                            }, this)\n                        ]\n                    }, void 0, true)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\logbook-dropdown.tsx\",\n                lineNumber: 152,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\components\\\\logbook-dropdown.tsx\",\n        lineNumber: 136,\n        columnNumber: 9\n    }, this);\n}\n_s(LogbookActionMenu, \"dAPSf+/dKcYv8C2tzKkPrutdw60=\", false, function() {\n    return [\n        nuqs__WEBPACK_IMPORTED_MODULE_9__.useQueryState,\n        next_navigation__WEBPACK_IMPORTED_MODULE_7__.useRouter\n    ];\n});\n_c = LogbookActionMenu;\nvar _c;\n$RefreshReg$(_c, \"LogbookActionMenu\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/logbook/components/logbook-dropdown.tsx\n"));

/***/ })

});