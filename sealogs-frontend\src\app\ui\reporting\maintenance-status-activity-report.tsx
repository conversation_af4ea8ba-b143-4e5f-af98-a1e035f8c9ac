'use client'

import Filter from '@/components/filter'
import React, { useMemo, useState } from 'react'
import ExportButton from './export-button'
import { GET_MAINTENANCE_STATUS_ACTIVITY_ENTRIES } from '@/app/lib/graphQL/query/reporting/GET_MAINTENANCE_STATUS_ACTIVITY_ENTRIES'
import { useLazyQuery } from '@apollo/client'
import { isOverDueTask } from '@/app/lib/actions'
import dayjs from 'dayjs'
import { isEmpty } from 'lodash'
import { exportCsv } from '@/app/helpers/csvHelper'
import { exportPdfTable } from '@/app/helpers/pdfHelper'
import { useRouter } from 'next/navigation'
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '@/components/ui/table'

import { ArrowLeft } from 'lucide-react'
import { Button, Card, CardContent, H3, ListHeader } from '@/components/ui'

interface DateRange {
    startDate: Date | null
    endDate: Date | null
}

interface IDropdownItem {
    label: string
    value: string
}

type FilterType = 'dateRange' | 'vessels' | 'category' | 'status' | 'member'
interface IFilter {
    type: FilterType
    data: any
}

const tableHeadings = [
    'Task Name',
    'Inventory',
    'Location',
    'Assigned To',
    'Status',
    'Due Date',
    'Due Status',
]

interface IReportItem {
    taskName: string
    inventoryName?: string
    vesselName?: string
    assignedTo?: string
    status?: string
    dueDate?: Date
    dueStatus: any
}

export default function MaintenanceStatusActivityReport() {
    const router = useRouter()
    const [selectedVessels, setSelectedVessels] = useState<IDropdownItem[]>([])
    const [category, setCategory] = useState<IDropdownItem | null>(null)
    const [status, setStatus] = useState<IDropdownItem | null>(null)
    const [crew, setCrew] = useState<IDropdownItem | null>(null)
    const [dateRange, setDateRange] = useState<DateRange>({
        startDate: new Date(),
        endDate: new Date(),
    })

    const [getReportData, { called, loading, data }] = useLazyQuery(
        GET_MAINTENANCE_STATUS_ACTIVITY_ENTRIES,
        {
            fetchPolicy: 'cache-and-network',
            onError: (error: any) => {
                console.error(
                    'GET_MAINTENANCE_STATUS_ACTIVITY_ENTRIES error',
                    error,
                )
            },
        },
    )

    const handleFilterOnChange = ({ type, data }: IFilter) => {
        switch (type) {
            case 'vessels':
                setSelectedVessels(data)
                break
            case 'category':
                setCategory(data)
                break

            case 'status':
                setStatus(data)
                break

            case 'dateRange':
                setDateRange(data || { startDate: null, endDate: null })
                break

            case 'member':
                setCrew(data)
                break

            default:
                break
        }
    }

    const generateReport = () => {
        const filter: any = {}

        if (
            dateRange &&
            dateRange.startDate !== null &&
            dateRange.endDate !== null
        ) {
            // Format dates as YYYY-MM-DD strings for GraphQL
            const startDateFormatted = dayjs(dateRange.startDate).format(
                'YYYY-MM-DD',
            )
            const endDateFormatted = dayjs(dateRange.endDate).format(
                'YYYY-MM-DD',
            )

            filter['expires'] = {
                gte: startDateFormatted,
                lte: endDateFormatted,
            }
        }

        if (selectedVessels.length > 0) {
            filter['basicComponentID'] = {
                in: selectedVessels.map((item) => item.value),
            }
        }

        if (category !== null) {
            filter['maintenanceCategoryID'] = {
                eq: category.value,
            }
        }

        if (status !== null) {
            filter['status'] = {
                eq: status.value,
            }
        }

        if (crew !== null) {
            filter['assignedToID'] = {
                eq: crew.value,
            }
        }

        getReportData({
            variables: {
                filter,
            },
        })
    }

    const downloadPdf = () => {
        if (reportData.length === 0) {
            return
        }

        const body = reportData.map((item: any) => {
            return [
                item.taskName,
                item.inventoryName ?? '',
                item.vesselName ?? '',
                item.assignedTo ?? '',
                item.status ?? '',
                item.dueDate ? dayjs(item.dueDate).format('DD/MM/YYYY') : '',
                dueStatusLabel(item.dueStatus),
            ]
        })

        const headers: any = [
            [
                'Task Name',
                'Inventory',
                'Location',
                'Assigned To',
                'Status',
                'Due Date',
                'Due Status',
            ],
        ]

        exportPdfTable({
            body,
            headers,
        })
    }

    const downloadCsv = () => {
        if (reportData.length === 0) {
            return
        }

        const csvEntries: string[][] = [
            [
                'task name',
                'inventory',
                'location',
                'assigned to',
                'status',
                'due date',
                'due status',
            ],
        ]

        reportData.forEach((item) => {
            csvEntries.push([
                item.taskName,
                item.inventoryName ?? 'N/A',
                item.vesselName ?? 'N/A',
                item.assignedTo ?? 'N/A',
                item.status ?? 'N/A',
                item.dueDate ? dayjs(item.dueDate).format('DD/MM/YYYY') : 'N/A',
                dueStatusLabel(item.dueStatus),
            ])
        })

        exportCsv(csvEntries)
    }

    const reportData = useMemo<IReportItem[]>(() => {
        const fetchedData = data?.readComponentMaintenanceChecks.nodes ?? []

        if (fetchedData.length === 0) {
            return []
        }

        const reportItems: IReportItem[] = []

        fetchedData.forEach((fetchedItem: any) => {
            const reportItem: IReportItem = {
                taskName: fetchedItem.name,
                vesselName: fetchedItem.basicComponent.title,
                assignedTo:
                    fetchedItem.assignedTo.id == 0
                        ? undefined
                        : `${fetchedItem.assignedTo.firstName} ${fetchedItem.assignedTo.surname}`,
                inventoryName: fetchedItem.inventory.title,
                dueDate: fetchedItem.expires
                    ? new Date(fetchedItem.expires)
                    : undefined,
                status: fetchedItem.status,
                dueStatus: isOverDueTask(fetchedItem),
            }
            reportItems.push(reportItem)
        })

        return reportItems
    }, [called, loading, data])

    return (
        <>
            <ListHeader
                title="Maintenance status and activity report"
                actions={
                    <Button
                        variant="back"
                        iconLeft={ArrowLeft}
                        onClick={() => router.push('/reporting')}>
                        Back
                    </Button>
                }
            />
            <Card className="mt-8">
                <CardContent className="flex flex-col gap-4">
                    <Filter
                        onChange={handleFilterOnChange}
                        onClick={generateReport}
                    />
                    <ExportButton
                        onDownloadPdf={downloadPdf}
                        onDownloadCsv={downloadCsv}
                    />
                    <Table>
                        <TableHeader>
                            <TableRow>
                                {tableHeadings.map((heading) => (
                                    <TableHead key={heading}>
                                        {heading}
                                    </TableHead>
                                ))}
                            </TableRow>
                        </TableHeader>
                        <TableContent
                            isLoading={called && loading}
                            reportData={reportData}
                        />
                    </Table>
                </CardContent>
            </Card>
        </>
    )
}

function TableContent({
    reportData,
    isLoading,
}: {
    reportData: IReportItem[]
    isLoading: boolean
}) {
    if (isLoading) {
        return (
            <TableBody>
                <TableRow>
                    <TableCell
                        colSpan={tableHeadings.length}
                        className="text-center  h-32">
                        Loading...
                    </TableCell>
                </TableRow>
            </TableBody>
        )
    }

    if (reportData.length == 0) {
        return (
            <TableBody>
                <TableRow>
                    <TableCell
                        colSpan={tableHeadings.length}
                        className="text-center  h-32">
                        No Data Available
                    </TableCell>
                </TableRow>
            </TableBody>
        )
    }

    return (
        <TableBody>
            {reportData.map((element: IReportItem, index: number) => {
                return (
                    <TableRow
                        key={`report-item-${index}`}
                        className={`group border-b  hover: `}>
                        <TableCell className="px-2 py-3 text-left w-[15%]">
                            <div className=" inline-block ml-3">
                                {element.taskName}
                            </div>
                        </TableCell>
                        <TableCell className="px-2 py-3 text-left w-[10%]">
                            <div className=" inline-block ">
                                {element.inventoryName}
                            </div>
                        </TableCell>
                        <TableCell className="px-2 py-3 text-left w-[10%]">
                            <div className=" inline-block ">
                                {element.vesselName}
                            </div>
                        </TableCell>
                        <TableCell className="px-2 py-3 text-left w-[10%]">
                            <div className=" inline-block ">
                                {element.assignedTo}
                            </div>
                        </TableCell>
                        <TableCell className="px-2 py-3 text-left w-[10%]">
                            <div className=" inline-block ">
                                {element.status}
                            </div>
                        </TableCell>
                        <TableCell className="px-2 py-3 text-left w-[10%]">
                            <div className=" inline-block ">
                                {element.dueDate
                                    ? dayjs(element.dueDate).format('DD/MM/YY')
                                    : ''}
                            </div>
                        </TableCell>
                        <TableCell className="px-2 py-3 text-left w-[10%]">
                            <div className=" inline-block ">
                                {dueStatusLabel(element.dueStatus)}
                            </div>
                        </TableCell>
                    </TableRow>
                )
            })}
        </TableBody>
    )
}

export const dueStatusLabel = (dueStatus: any): string => {
    return `${
        dueStatus?.status &&
        ['High', 'Medium', 'Low'].includes(dueStatus.status)
            ? dueStatus?.days
            : ''
    }${
        dueStatus?.status === 'Completed' && dueStatus?.days === 'Save As Draft'
            ? dueStatus?.days
            : ''
    }${dueStatus?.status === 'Upcoming' ? dueStatus?.days : ''}${
        dueStatus?.status === 'Completed' && isEmpty(dueStatus?.days)
            ? dueStatus?.status
            : ''
    }${
        dueStatus?.status === 'Completed' &&
        !isEmpty(dueStatus?.days) &&
        dueStatus?.days !== 'Save As Draft'
            ? dueStatus?.days
            : ''
    }`
}
