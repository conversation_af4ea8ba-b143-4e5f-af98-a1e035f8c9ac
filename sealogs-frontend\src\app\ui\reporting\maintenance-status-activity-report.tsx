'use client'

import React, { useMemo, useState } from 'react'
import ExportButton from './export-button'
import { GET_MAINTENANCE_STATUS_ACTIVITY_ENTRIES } from '@/app/lib/graphQL/query/reporting/GET_MAINTENANCE_STATUS_ACTIVITY_ENTRIES'
import { useLazyQuery } from '@apollo/client'
import { isOverDueTask } from '@/app/lib/actions'
import dayjs from 'dayjs'
import { isEmpty } from 'lodash'
import { exportCsv } from '@/app/helpers/csvHelper'
import { exportPdfTable } from '@/app/helpers/pdfHelper'
import { useRouter } from 'next/navigation'
import { createColumns, DataTable, RowStatus } from '@/components/filteredTable'
import { DataTableSortHeader } from '@/components/data-table-sort-header'
import { StatusBadge } from '@/app/ui/maintenance/list/list'

import { ArrowLeft } from 'lucide-react'
import { <PERSON><PERSON>, <PERSON>, Card<PERSON>ontent, ListHeader } from '@/components/ui'

interface DateRange {
    startDate: Date | null
    endDate: Date | null
}

interface IDropdownItem {
    label: string
    value: string
}

type FilterType = 'dateRange' | 'vessels' | 'category' | 'status' | 'member'
interface IFilter {
    type: FilterType
    data: any
}

interface IReportItem {
    taskName: string
    inventoryName?: string
    vesselName?: string
    assignedTo?: string
    status?: string
    dueDate?: Date
    dueStatus: any
}

// Helper function to extract status text using the exact same logic as StatusBadge
// This ensures consistency between visual display and exported data
const getStatusText = (isOverDue: any): string => {
    let statusText = ''
    if (
        isOverDue?.status &&
        ['High', 'Medium', 'Low'].includes(isOverDue.status)
    ) {
        statusText = isOverDue?.days
    } else if (
        isOverDue?.status === 'Completed' &&
        isOverDue?.days === 'Save As Draft'
    ) {
        statusText = isOverDue?.days
    } else if (isOverDue?.status === 'Upcoming') {
        statusText = isOverDue?.days
    } else if (isOverDue?.status === 'Completed' && isEmpty(isOverDue?.days)) {
        statusText = isOverDue?.status
    } else if (
        isOverDue?.status === 'Completed' &&
        !isEmpty(isOverDue?.days) &&
        isOverDue?.days !== 'Save As Draft'
    ) {
        statusText = isOverDue?.days
    }
    return statusText || ''
}

// Helper function to create a compatible MaintenanceCheck object for StatusBadge
const createMaintenanceCheckForBadge = (reportItem: IReportItem) => {
    return {
        id: 0, // Not needed for display
        assignedTo: { id: 0, name: '' }, // Not needed for display
        basicComponent: { id: 0, title: null }, // Not needed for display
        inventory: { id: 0, item: null }, // Not needed for display
        status: reportItem.status || '',
        recurringID: 0, // Not needed for display
        name: reportItem.taskName,
        created: '', // Not needed for display
        severity: '', // Not needed for display
        isOverDue: reportItem.dueStatus, // This is the key property StatusBadge needs
        comments: null, // Not needed for display
        workOrderNumber: null, // Not needed for display
        startDate: '', // Not needed for display
        expires: null, // Not needed for display
        maintenanceCategoryID: 0, // Not needed for display
    }
}

// Column definitions for the DataTable
const columns = createColumns<IReportItem>([
    {
        accessorKey: 'taskName',
        header: ({ column }: { column: any }) => (
            <DataTableSortHeader column={column} title="Task Name" />
        ),
        cell: ({ row }: { row: any }) => {
            const item = row.original
            return <div className="font-medium">{item.taskName}</div>
        },
    },
    {
        accessorKey: 'inventoryName',
        header: ({ column }: { column: any }) => (
            <DataTableSortHeader column={column} title="Inventory" />
        ),
        cell: ({ row }: { row: any }) => {
            const item = row.original
            return <div>{item.inventoryName || ''}</div>
        },
    },
    {
        accessorKey: 'vesselName',
        header: ({ column }: { column: any }) => (
            <DataTableSortHeader column={column} title="Location" />
        ),
        cell: ({ row }: { row: any }) => {
            const item = row.original
            return <div>{item.vesselName || ''}</div>
        },
    },
    {
        accessorKey: 'assignedTo',
        header: ({ column }: { column: any }) => (
            <DataTableSortHeader column={column} title="Assigned To" />
        ),
        cell: ({ row }: { row: any }) => {
            const item = row.original
            return <div>{item.assignedTo || ''}</div>
        },
    },
    {
        accessorKey: 'status',
        header: ({ column }: { column: any }) => (
            <DataTableSortHeader column={column} title="Status" />
        ),
        cell: ({ row }: { row: any }) => {
            const item = row.original
            return <div>{item.status || ''}</div>
        },
    },
    {
        accessorKey: 'dueDate',
        header: ({ column }: { column: any }) => (
            <DataTableSortHeader column={column} title="Due Date" />
        ),
        cell: ({ row }: { row: any }) => {
            const item = row.original
            return (
                <div>
                    {item.dueDate ? dayjs(item.dueDate).format('DD/MM/YY') : ''}
                </div>
            )
        },
    },
    {
        accessorKey: 'dueStatus',
        header: ({ column }: { column: any }) => (
            <DataTableSortHeader column={column} title="Due Status" />
        ),
        cell: ({ row }: { row: any }) => {
            const item = row.original
            const maintenanceCheck = createMaintenanceCheckForBadge(item)
            return <StatusBadge maintenanceCheck={maintenanceCheck} />
        },
    },
])

// Function to evaluate row status for highlighting
const getRowStatus = (rowData: IReportItem): RowStatus => {
    if (rowData.dueStatus?.status === 'High') {
        return 'overdue'
    }
    if (
        rowData.dueStatus?.status === 'Medium' ||
        rowData.dueStatus?.status === 'Low'
    ) {
        return 'upcoming'
    }
    return 'normal'
}

export default function MaintenanceStatusActivityReport() {
    const router = useRouter()
    const [selectedVessels, setSelectedVessels] = useState<IDropdownItem[]>([])
    const [category, setCategory] = useState<IDropdownItem | null>(null)
    const [status, setStatus] = useState<IDropdownItem | null>(null)
    const [crew, setCrew] = useState<IDropdownItem | null>(null)
    const [dateRange, setDateRange] = useState<DateRange>({
        startDate: new Date(),
        endDate: new Date(),
    })

    const [getReportData, { called, loading, data }] = useLazyQuery(
        GET_MAINTENANCE_STATUS_ACTIVITY_ENTRIES,
        {
            fetchPolicy: 'cache-and-network',
            onError: (error: any) => {
                console.error(
                    'GET_MAINTENANCE_STATUS_ACTIVITY_ENTRIES error',
                    error,
                )
            },
        },
    )

    const handleFilterOnChange = ({ type, data }: IFilter) => {
        switch (type) {
            case 'vessels':
                setSelectedVessels(data)
                break
            case 'category':
                setCategory(data)
                break

            case 'status':
                setStatus(data)
                break

            case 'dateRange':
                setDateRange(data || { startDate: null, endDate: null })
                break

            case 'member':
                setCrew(data)
                break

            default:
                break
        }
    }

    const generateReport = () => {
        const filter: any = {}

        if (
            dateRange &&
            dateRange.startDate !== null &&
            dateRange.endDate !== null
        ) {
            // Format dates as YYYY-MM-DD strings for GraphQL
            const startDateFormatted = dayjs(dateRange.startDate).format(
                'YYYY-MM-DD',
            )
            const endDateFormatted = dayjs(dateRange.endDate).format(
                'YYYY-MM-DD',
            )

            filter['expires'] = {
                gte: startDateFormatted,
                lte: endDateFormatted,
            }
        }

        if (selectedVessels.length > 0) {
            filter['basicComponentID'] = {
                in: selectedVessels.map((item) => item.value),
            }
        }

        if (category !== null) {
            filter['maintenanceCategoryID'] = {
                eq: category.value,
            }
        }

        if (status !== null) {
            filter['status'] = {
                eq: status.value,
            }
        }

        if (crew !== null) {
            filter['assignedToID'] = {
                eq: crew.value,
            }
        }

        getReportData({
            variables: {
                filter,
            },
        })
    }

    const downloadPdf = () => {
        if (reportData.length === 0) {
            return
        }

        const body = reportData.map((item: any) => {
            return [
                item.taskName,
                item.inventoryName ?? '',
                item.vesselName ?? '',
                item.assignedTo ?? '',
                item.status ?? '',
                item.dueDate ? dayjs(item.dueDate).format('DD/MM/YYYY') : '',
                getStatusText(item.dueStatus),
            ]
        })

        const headers: any = [
            [
                'Task Name',
                'Inventory',
                'Location',
                'Assigned To',
                'Status',
                'Due Date',
                'Due Status',
            ],
        ]

        exportPdfTable({
            body,
            headers,
        })
    }

    const downloadCsv = () => {
        if (reportData.length === 0) {
            return
        }

        const csvEntries: string[][] = [
            [
                'task name',
                'inventory',
                'location',
                'assigned to',
                'status',
                'due date',
                'due status',
            ],
        ]

        reportData.forEach((item) => {
            csvEntries.push([
                item.taskName,
                item.inventoryName ?? 'N/A',
                item.vesselName ?? 'N/A',
                item.assignedTo ?? 'N/A',
                item.status ?? 'N/A',
                item.dueDate ? dayjs(item.dueDate).format('DD/MM/YYYY') : 'N/A',
                getStatusText(item.dueStatus),
            ])
        })

        exportCsv(csvEntries)
    }

    const reportData = useMemo<IReportItem[]>(() => {
        const fetchedData = data?.readComponentMaintenanceChecks.nodes ?? []

        if (fetchedData.length === 0) {
            return []
        }

        const reportItems: IReportItem[] = []

        fetchedData.forEach((fetchedItem: any) => {
            const reportItem: IReportItem = {
                taskName: fetchedItem.name,
                vesselName: fetchedItem.basicComponent.title,
                assignedTo:
                    fetchedItem.assignedTo.id == 0
                        ? undefined
                        : `${fetchedItem.assignedTo.firstName} ${fetchedItem.assignedTo.surname}`,
                inventoryName: fetchedItem.inventory.title,
                dueDate: fetchedItem.expires
                    ? new Date(fetchedItem.expires)
                    : undefined,
                status: fetchedItem.status,
                dueStatus: isOverDueTask(fetchedItem),
            }
            reportItems.push(reportItem)
        })

        return reportItems
    }, [called, loading, data])

    return (
        <>
            <ListHeader
                title="Maintenance status and activity report"
                actions={
                    <Button
                        variant="back"
                        iconLeft={ArrowLeft}
                        onClick={() => router.push('/reporting')}>
                        Back
                    </Button>
                }
            />
            <div className="mt-8 space-y-4">
                <Card>
                    <CardContent className="flex flex-col gap-4">
                        <ExportButton
                            onDownloadPdf={downloadPdf}
                            onDownloadCsv={downloadCsv}
                        />
                    </CardContent>
                </Card>
                <DataTable
                    columns={columns}
                    data={reportData}
                    isLoading={called && loading}
                    rowStatus={getRowStatus}
                    onChange={handleFilterOnChange}
                    onFilterClick={generateReport}
                    showToolbar={true}
                />
            </div>
        </>
    )
}

export const dueStatusLabel = (dueStatus: any): string => {
    return `${
        dueStatus?.status &&
        ['High', 'Medium', 'Low'].includes(dueStatus.status)
            ? dueStatus?.days
            : ''
    }${
        dueStatus?.status === 'Completed' && dueStatus?.days === 'Save As Draft'
            ? dueStatus?.days
            : ''
    }${dueStatus?.status === 'Upcoming' ? dueStatus?.days : ''}${
        dueStatus?.status === 'Completed' && isEmpty(dueStatus?.days)
            ? dueStatus?.status
            : ''
    }${
        dueStatus?.status === 'Completed' &&
        !isEmpty(dueStatus?.days) &&
        dueStatus?.days !== 'Save As Draft'
            ? dueStatus?.days
            : ''
    }`
}
