"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/vessel/info/page",{

/***/ "(app-pages-browser)/./src/components/filter/components/maintenance-report-actions.tsx":
/*!*************************************************************************!*\
  !*** ./src/components/filter/components/maintenance-report-actions.tsx ***!
  \*************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MaintenanceReportFilterActions: function() { return /* binding */ MaintenanceReportFilterActions; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./src/components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/sidebar */ \"(app-pages-browser)/./src/components/ui/sidebar.tsx\");\n/* harmony import */ var _app_lib_icons__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/lib/icons */ \"(app-pages-browser)/./src/app/lib/icons/index.ts\");\n/* harmony import */ var _barrel_optimize_names_DownloadIcon_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=DownloadIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ MaintenanceReportFilterActions auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst MaintenanceReportFilterActions = (param)=>{\n    let { onDownloadCsv, onDownloadPdf } = param;\n    _s();\n    const { isMobile } = (0,_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.useSidebar)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_5__.useRouter)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.DropdownMenu, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.DropdownMenuTrigger, {\n                asChild: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_lib_icons__WEBPACK_IMPORTED_MODULE_4__.SealogsCogIcon, {\n                    size: 36\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\filter\\\\components\\\\maintenance-report-actions.tsx\",\n                    lineNumber: 32,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\filter\\\\components\\\\maintenance-report-actions.tsx\",\n                lineNumber: 31,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.DropdownMenuContent, {\n                side: isMobile ? \"bottom\" : \"right\",\n                align: isMobile ? \"end\" : \"start\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-input flex flex-col items-center justify-center py-[9px]\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.DropdownMenuItem, {\n                            variant: \"backButton\",\n                            onClick: ()=>router.push(\"/reporting\"),\n                            children: \"Back\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\filter\\\\components\\\\maintenance-report-actions.tsx\",\n                            lineNumber: 38,\n                            columnNumber: 21\n                        }, undefined),\n                        onDownloadPdf && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.DropdownMenuItem, {\n                            onClick: onDownloadPdf,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DownloadIcon_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\filter\\\\components\\\\maintenance-report-actions.tsx\",\n                                    lineNumber: 45,\n                                    columnNumber: 29\n                                }, undefined),\n                                \"Download PDF\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\filter\\\\components\\\\maintenance-report-actions.tsx\",\n                            lineNumber: 44,\n                            columnNumber: 25\n                        }, undefined),\n                        onDownloadCsv && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.DropdownMenuItem, {\n                            onClick: onDownloadCsv,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DownloadIcon_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\filter\\\\components\\\\maintenance-report-actions.tsx\",\n                                    lineNumber: 51,\n                                    columnNumber: 29\n                                }, undefined),\n                                \"Download CSV\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\filter\\\\components\\\\maintenance-report-actions.tsx\",\n                            lineNumber: 50,\n                            columnNumber: 25\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\filter\\\\components\\\\maintenance-report-actions.tsx\",\n                    lineNumber: 37,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\filter\\\\components\\\\maintenance-report-actions.tsx\",\n                lineNumber: 34,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\filter\\\\components\\\\maintenance-report-actions.tsx\",\n        lineNumber: 30,\n        columnNumber: 9\n    }, undefined);\n};\n_s(MaintenanceReportFilterActions, \"2jIoXD9G8OZZK/Hd0W/SlED0TzQ=\", false, function() {\n    return [\n        _components_ui_sidebar__WEBPACK_IMPORTED_MODULE_3__.useSidebar,\n        next_navigation__WEBPACK_IMPORTED_MODULE_5__.useRouter\n    ];\n});\n_c = MaintenanceReportFilterActions;\nvar _c;\n$RefreshReg$(_c, \"MaintenanceReportFilterActions\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/filter/components/maintenance-report-actions.tsx\n"));

/***/ })

});