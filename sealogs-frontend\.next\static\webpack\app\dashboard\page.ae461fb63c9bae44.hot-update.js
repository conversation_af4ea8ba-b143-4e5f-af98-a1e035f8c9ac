"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/ui/reporting/maintenance-status-activity-report.tsx":
/*!*********************************************************************!*\
  !*** ./src/app/ui/reporting/maintenance-status-activity-report.tsx ***!
  \*********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ MaintenanceStatusActivityReport; },\n/* harmony export */   dueStatusLabel: function() { return /* binding */ dueStatusLabel; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _app_lib_graphQL_query_reporting_GET_MAINTENANCE_STATUS_ACTIVITY_ENTRIES__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/lib/graphQL/query/reporting/GET_MAINTENANCE_STATUS_ACTIVITY_ENTRIES */ \"(app-pages-browser)/./src/app/lib/graphQL/query/reporting/GET_MAINTENANCE_STATUS_ACTIVITY_ENTRIES.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _app_lib_actions__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/lib/actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! lodash/isEmpty */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isEmpty.js\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(lodash_isEmpty__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _app_helpers_csvHelper__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/app/helpers/csvHelper */ \"(app-pages-browser)/./src/app/helpers/csvHelper.ts\");\n/* harmony import */ var _app_helpers_pdfHelper__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/helpers/pdfHelper */ \"(app-pages-browser)/./src/app/helpers/pdfHelper.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_filteredTable__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/filteredTable */ \"(app-pages-browser)/./src/components/filteredTable.tsx\");\n/* harmony import */ var _components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/data-table-sort-header */ \"(app-pages-browser)/./src/components/data-table-sort-header.tsx\");\n/* harmony import */ var _components_filter_components_maintenance_report_actions__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/filter/components/maintenance-report-actions */ \"(app-pages-browser)/./src/components/filter/components/maintenance-report-actions.tsx\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default,dueStatusLabel auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Column definitions for the DataTable\nconst columns = (0,_components_filteredTable__WEBPACK_IMPORTED_MODULE_9__.createColumns)([\n    {\n        accessorKey: \"taskName\",\n        header: (param)=>{\n            let { column } = param;\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_10__.DataTableSortHeader, {\n                column: column,\n                title: \"Task Name\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                lineNumber: 50,\n                columnNumber: 13\n            }, undefined);\n        },\n        cell: (param)=>{\n            let { row } = param;\n            const item = row.original;\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"font-medium\",\n                children: item.taskName\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                lineNumber: 54,\n                columnNumber: 20\n            }, undefined);\n        }\n    },\n    {\n        accessorKey: \"inventoryName\",\n        header: (param)=>{\n            let { column } = param;\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_10__.DataTableSortHeader, {\n                column: column,\n                title: \"Inventory\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                lineNumber: 60,\n                columnNumber: 13\n            }, undefined);\n        },\n        cell: (param)=>{\n            let { row } = param;\n            const item = row.original;\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: item.inventoryName || \"\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                lineNumber: 64,\n                columnNumber: 20\n            }, undefined);\n        }\n    },\n    {\n        accessorKey: \"vesselName\",\n        header: (param)=>{\n            let { column } = param;\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_10__.DataTableSortHeader, {\n                column: column,\n                title: \"Location\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                lineNumber: 70,\n                columnNumber: 13\n            }, undefined);\n        },\n        cell: (param)=>{\n            let { row } = param;\n            const item = row.original;\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: item.vesselName || \"\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                lineNumber: 74,\n                columnNumber: 20\n            }, undefined);\n        }\n    },\n    {\n        accessorKey: \"assignedTo\",\n        header: (param)=>{\n            let { column } = param;\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_10__.DataTableSortHeader, {\n                column: column,\n                title: \"Assigned To\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                lineNumber: 80,\n                columnNumber: 13\n            }, undefined);\n        },\n        cell: (param)=>{\n            let { row } = param;\n            const item = row.original;\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: item.assignedTo || \"\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                lineNumber: 84,\n                columnNumber: 20\n            }, undefined);\n        }\n    },\n    {\n        accessorKey: \"status\",\n        header: (param)=>{\n            let { column } = param;\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_10__.DataTableSortHeader, {\n                column: column,\n                title: \"Status\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                lineNumber: 90,\n                columnNumber: 13\n            }, undefined);\n        },\n        cell: (param)=>{\n            let { row } = param;\n            const item = row.original;\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: item.status || \"\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                lineNumber: 94,\n                columnNumber: 20\n            }, undefined);\n        }\n    },\n    {\n        accessorKey: \"dueDate\",\n        header: (param)=>{\n            let { column } = param;\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_10__.DataTableSortHeader, {\n                column: column,\n                title: \"Due Date\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                lineNumber: 100,\n                columnNumber: 13\n            }, undefined);\n        },\n        cell: (param)=>{\n            let { row } = param;\n            const item = row.original;\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: item.dueDate ? dayjs__WEBPACK_IMPORTED_MODULE_4___default()(item.dueDate).format(\"DD/MM/YY\") : \"\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                lineNumber: 105,\n                columnNumber: 17\n            }, undefined);\n        }\n    },\n    {\n        accessorKey: \"dueStatus\",\n        header: (param)=>{\n            let { column } = param;\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_10__.DataTableSortHeader, {\n                column: column,\n                title: \"Due Status\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                lineNumber: 114,\n                columnNumber: 13\n            }, undefined);\n        },\n        cell: (param)=>{\n            let { row } = param;\n            const item = row.original;\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: dueStatusLabel(item.dueStatus)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                lineNumber: 118,\n                columnNumber: 20\n            }, undefined);\n        }\n    }\n]);\n// Function to evaluate row status for highlighting\nconst getRowStatus = (rowData)=>{\n    var _rowData_dueStatus, _rowData_dueStatus1, _rowData_dueStatus2;\n    if (((_rowData_dueStatus = rowData.dueStatus) === null || _rowData_dueStatus === void 0 ? void 0 : _rowData_dueStatus.status) === \"High\") {\n        return \"overdue\";\n    }\n    if (((_rowData_dueStatus1 = rowData.dueStatus) === null || _rowData_dueStatus1 === void 0 ? void 0 : _rowData_dueStatus1.status) === \"Medium\" || ((_rowData_dueStatus2 = rowData.dueStatus) === null || _rowData_dueStatus2 === void 0 ? void 0 : _rowData_dueStatus2.status) === \"Low\") {\n        return \"upcoming\";\n    }\n    return \"normal\";\n};\nfunction MaintenanceStatusActivityReport() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_8__.useRouter)();\n    const [selectedVessels, setSelectedVessels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [category, setCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [crew, setCrew] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [dateRange, setDateRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        startDate: new Date(),\n        endDate: new Date()\n    });\n    const [getReportData, { called, loading, data }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_13__.useLazyQuery)(_app_lib_graphQL_query_reporting_GET_MAINTENANCE_STATUS_ACTIVITY_ENTRIES__WEBPACK_IMPORTED_MODULE_2__.GET_MAINTENANCE_STATUS_ACTIVITY_ENTRIES, {\n        fetchPolicy: \"cache-and-network\",\n        onError: (error)=>{\n            console.error(\"GET_MAINTENANCE_STATUS_ACTIVITY_ENTRIES error\", error);\n        }\n    });\n    const handleFilterOnChange = (param)=>{\n        let { type, data } = param;\n        switch(type){\n            case \"vessels\":\n                setSelectedVessels(data);\n                break;\n            case \"category\":\n                setCategory(data);\n                break;\n            case \"status\":\n                setStatus(data);\n                break;\n            case \"dateRange\":\n                setDateRange(data || {\n                    startDate: null,\n                    endDate: null\n                });\n                break;\n            case \"member\":\n                setCrew(data);\n                break;\n            default:\n                break;\n        }\n    };\n    const generateReport = ()=>{\n        const filter = {};\n        if (dateRange && dateRange.startDate !== null && dateRange.endDate !== null) {\n            // Format dates as YYYY-MM-DD strings for GraphQL\n            const startDateFormatted = dayjs__WEBPACK_IMPORTED_MODULE_4___default()(dateRange.startDate).format(\"YYYY-MM-DD\");\n            const endDateFormatted = dayjs__WEBPACK_IMPORTED_MODULE_4___default()(dateRange.endDate).format(\"YYYY-MM-DD\");\n            filter[\"expires\"] = {\n                gte: startDateFormatted,\n                lte: endDateFormatted\n            };\n        }\n        if (selectedVessels.length > 0) {\n            filter[\"basicComponentID\"] = {\n                in: selectedVessels.map((item)=>item.value)\n            };\n        }\n        if (category !== null) {\n            filter[\"maintenanceCategoryID\"] = {\n                eq: category.value\n            };\n        }\n        if (status !== null) {\n            filter[\"status\"] = {\n                eq: status.value\n            };\n        }\n        if (crew !== null) {\n            filter[\"assignedToID\"] = {\n                eq: crew.value\n            };\n        }\n        getReportData({\n            variables: {\n                filter\n            }\n        });\n    };\n    const downloadPdf = ()=>{\n        if (reportData.length === 0) {\n            return;\n        }\n        const body = reportData.map((item)=>{\n            var _item_inventoryName, _item_vesselName, _item_assignedTo, _item_status;\n            return [\n                item.taskName,\n                (_item_inventoryName = item.inventoryName) !== null && _item_inventoryName !== void 0 ? _item_inventoryName : \"\",\n                (_item_vesselName = item.vesselName) !== null && _item_vesselName !== void 0 ? _item_vesselName : \"\",\n                (_item_assignedTo = item.assignedTo) !== null && _item_assignedTo !== void 0 ? _item_assignedTo : \"\",\n                (_item_status = item.status) !== null && _item_status !== void 0 ? _item_status : \"\",\n                item.dueDate ? dayjs__WEBPACK_IMPORTED_MODULE_4___default()(item.dueDate).format(\"DD/MM/YYYY\") : \"\",\n                dueStatusLabel(item.dueStatus)\n            ];\n        });\n        const headers = [\n            [\n                \"Task Name\",\n                \"Inventory\",\n                \"Location\",\n                \"Assigned To\",\n                \"Status\",\n                \"Due Date\",\n                \"Due Status\"\n            ]\n        ];\n        (0,_app_helpers_pdfHelper__WEBPACK_IMPORTED_MODULE_7__.exportPdfTable)({\n            body,\n            headers\n        });\n    };\n    const downloadCsv = ()=>{\n        if (reportData.length === 0) {\n            return;\n        }\n        const csvEntries = [\n            [\n                \"task name\",\n                \"inventory\",\n                \"location\",\n                \"assigned to\",\n                \"status\",\n                \"due date\",\n                \"due status\"\n            ]\n        ];\n        reportData.forEach((item)=>{\n            var _item_inventoryName, _item_vesselName, _item_assignedTo, _item_status;\n            csvEntries.push([\n                item.taskName,\n                (_item_inventoryName = item.inventoryName) !== null && _item_inventoryName !== void 0 ? _item_inventoryName : \"N/A\",\n                (_item_vesselName = item.vesselName) !== null && _item_vesselName !== void 0 ? _item_vesselName : \"N/A\",\n                (_item_assignedTo = item.assignedTo) !== null && _item_assignedTo !== void 0 ? _item_assignedTo : \"N/A\",\n                (_item_status = item.status) !== null && _item_status !== void 0 ? _item_status : \"N/A\",\n                item.dueDate ? dayjs__WEBPACK_IMPORTED_MODULE_4___default()(item.dueDate).format(\"DD/MM/YYYY\") : \"N/A\",\n                dueStatusLabel(item.dueStatus)\n            ]);\n        });\n        (0,_app_helpers_csvHelper__WEBPACK_IMPORTED_MODULE_6__.exportCsv)(csvEntries);\n    };\n    const reportData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        var _data_readComponentMaintenanceChecks_nodes;\n        const fetchedData = (_data_readComponentMaintenanceChecks_nodes = data === null || data === void 0 ? void 0 : data.readComponentMaintenanceChecks.nodes) !== null && _data_readComponentMaintenanceChecks_nodes !== void 0 ? _data_readComponentMaintenanceChecks_nodes : [];\n        if (fetchedData.length === 0) {\n            return [];\n        }\n        const reportItems = [];\n        fetchedData.forEach((fetchedItem)=>{\n            const reportItem = {\n                taskName: fetchedItem.name,\n                vesselName: fetchedItem.basicComponent.title,\n                assignedTo: fetchedItem.assignedTo.id == 0 ? undefined : \"\".concat(fetchedItem.assignedTo.firstName, \" \").concat(fetchedItem.assignedTo.surname),\n                inventoryName: fetchedItem.inventory.title,\n                dueDate: fetchedItem.expires ? new Date(fetchedItem.expires) : undefined,\n                status: fetchedItem.status,\n                dueStatus: (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_3__.isOverDueTask)(fetchedItem)\n            };\n            reportItems.push(reportItem);\n        });\n        return reportItems;\n    }, [\n        called,\n        loading,\n        data\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.ListHeader, {\n                title: \"Maintenance status and activity report\",\n                actions: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filter_components_maintenance_report_actions__WEBPACK_IMPORTED_MODULE_11__.MaintenanceReportFilterActions, {\n                    onDownloadPdf: downloadPdf,\n                    onDownloadCsv: downloadCsv\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                    lineNumber: 342,\n                    columnNumber: 21\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                lineNumber: 339,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filteredTable__WEBPACK_IMPORTED_MODULE_9__.DataTable, {\n                    columns: columns,\n                    data: reportData,\n                    isLoading: called && loading,\n                    rowStatus: getRowStatus,\n                    onChange: handleFilterOnChange,\n                    onFilterClick: generateReport,\n                    showToolbar: true\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                    lineNumber: 349,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                lineNumber: 348,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(MaintenanceStatusActivityReport, \"oU/hWC7Aj40C5AHa8r/sGIGstSo=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_8__.useRouter,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_13__.useLazyQuery\n    ];\n});\n_c = MaintenanceStatusActivityReport;\nconst dueStatusLabel = (dueStatus)=>{\n    return \"\".concat((dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.status) && [\n        \"High\",\n        \"Medium\",\n        \"Low\"\n    ].includes(dueStatus.status) ? dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.days : \"\").concat((dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.status) === \"Completed\" && (dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.days) === \"Save As Draft\" ? dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.days : \"\").concat((dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.status) === \"Upcoming\" ? dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.days : \"\").concat((dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.status) === \"Completed\" && lodash_isEmpty__WEBPACK_IMPORTED_MODULE_5___default()(dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.days) ? dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.status : \"\").concat((dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.status) === \"Completed\" && !lodash_isEmpty__WEBPACK_IMPORTED_MODULE_5___default()(dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.days) && (dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.days) !== \"Save As Draft\" ? dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.days : \"\");\n};\nvar _c;\n$RefreshReg$(_c, \"MaintenanceStatusActivityReport\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/reporting/maintenance-status-activity-report.tsx\n"));

/***/ })

});